package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.core.classloader.PluginClassLoaderManager;
import com.talkweb.ai.indexer.core.lifecycle.PluginLifecycleManager;
import com.talkweb.ai.indexer.core.loader.PluginLoader;
import com.talkweb.ai.indexer.core.registry.PluginRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 重构后的DefaultPluginManager测试
 */
public class RefactoredDefaultPluginManagerTest {

    @Mock
    private PluginConfig config;
    
    @Mock
    private PluginLoader pluginLoader;
    
    @Mock
    private PluginLifecycleManager lifecycleManager;
    
    @Mock
    private PluginClassLoaderManager classLoaderManager;
    
    @Mock
    private PluginRegistry pluginRegistry;
    
    @Mock
    private Plugin mockPlugin;
    
    @Mock
    private PluginMetadata mockMetadata;

    private DefaultPluginManager pluginManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置基本的mock行为
        when(config.getPluginsDir()).thenReturn(Paths.get("test-plugins"));
        when(config.getDebounceTime()).thenReturn(500L);
        when(config.getPollInterval()).thenReturn(5000L);
        
        when(mockMetadata.getId()).thenReturn("test-plugin");
        when(mockMetadata.getVersion()).thenReturn("1.0.0");
        when(mockPlugin.getMetadata()).thenReturn(mockMetadata);
        when(mockPlugin.getState()).thenReturn(PluginState.LOADED);
        
        pluginManager = new DefaultPluginManager(config, pluginLoader, lifecycleManager, 
                                               classLoaderManager, pluginRegistry);
    }

    @Test
    void testInstallPlugin() throws Exception {
        // Arrange
        Path pluginPath = Paths.get("test-plugin.jar");
        when(pluginLoader.extractMetadata(pluginPath)).thenReturn(mockMetadata);
        when(pluginLoader.loadPlugin(pluginPath)).thenReturn(mockPlugin);
        when(pluginRegistry.isPluginRegistered("test-plugin")).thenReturn(false);

        // Act
        pluginManager.installPlugin(pluginPath, false);

        // Assert
        verify(pluginLoader).extractMetadata(pluginPath);
        verify(pluginLoader).loadPlugin(pluginPath);
        verify(pluginRegistry).registerPlugin(mockPlugin);
    }

    @Test
    void testInstallPluginWithForce() throws Exception {
        // Arrange
        Path pluginPath = Paths.get("test-plugin.jar");
        when(pluginLoader.extractMetadata(pluginPath)).thenReturn(mockMetadata);
        when(pluginLoader.loadPlugin(pluginPath)).thenReturn(mockPlugin);
        when(pluginRegistry.isPluginRegistered("test-plugin")).thenReturn(true);
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));

        // Act
        pluginManager.installPlugin(pluginPath, true);

        // Assert
        verify(pluginLoader).extractMetadata(pluginPath);
        verify(pluginLoader).loadPlugin(pluginPath);
        verify(pluginRegistry).registerPlugin(mockPlugin);
        // 验证先卸载了现有插件
        verify(lifecycleManager).destroyPlugin(mockPlugin);
        verify(pluginRegistry).unregisterPlugin("test-plugin");
        verify(classLoaderManager).removeClassLoader("test-plugin");
    }

    @Test
    void testRegisterPlugin() throws Exception {
        // Act
        pluginManager.registerPlugin(mockPlugin);

        // Assert
        verify(pluginRegistry).registerPlugin(mockPlugin);
    }

    @Test
    void testGetPlugin() {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));

        // Act
        Optional<Plugin> result = pluginManager.getPlugin("test-plugin");

        // Assert
        assertTrue(result.isPresent());
        assertEquals(mockPlugin, result.get());
        verify(pluginRegistry).getPlugin("test-plugin");
    }

    @Test
    void testGetPluginWithVersion() {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin", "1.0.0")).thenReturn(Optional.of(mockPlugin));

        // Act
        Optional<Plugin> result = pluginManager.getPlugin("test-plugin", "1.0.0");

        // Assert
        assertTrue(result.isPresent());
        assertEquals(mockPlugin, result.get());
        verify(pluginRegistry).getPlugin("test-plugin", "1.0.0");
    }

    @Test
    void testGetPlugins() {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        var result = pluginManager.getPlugins();

        // Assert
        assertEquals(1, result.size());
        assertTrue(result.contains(mockPlugin));
        verify(pluginRegistry).getAllPlugins();
    }

    @Test
    void testInitPlugins() throws Exception {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        pluginManager.initPlugins();

        // Assert
        verify(lifecycleManager).initializePlugins(eq(Arrays.asList(mockPlugin)), any(PluginContext.class));
    }

    @Test
    void testStartPlugins() throws Exception {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        pluginManager.startPlugins();

        // Assert
        verify(lifecycleManager).startPlugins(Arrays.asList(mockPlugin));
    }

    @Test
    void testStopPlugins() throws Exception {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        pluginManager.stopPlugins();

        // Assert
        verify(lifecycleManager).stopPlugins(Arrays.asList(mockPlugin));
    }

    @Test
    void testDestroyPlugins() throws Exception {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        pluginManager.destroyPlugins();

        // Assert
        verify(lifecycleManager).destroyPlugins(Arrays.asList(mockPlugin));
        verify(pluginRegistry).clear();
        verify(classLoaderManager).clearAllClassLoaders();
    }

    @Test
    void testUninstallPlugin() throws Exception {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));
        when(mockPlugin.getState()).thenReturn(PluginState.STOPPED);
        when(pluginRegistry.unregisterPlugin("test-plugin")).thenReturn(true);

        // Act
        boolean result = pluginManager.uninstallPlugin("test-plugin", false);

        // Assert
        assertTrue(result);
        verify(lifecycleManager).destroyPlugin(mockPlugin);
        verify(pluginRegistry).unregisterPlugin("test-plugin");
        verify(classLoaderManager).removeClassLoader("test-plugin");
    }

    @Test
    void testUninstallRunningPluginWithoutForce() {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));
        when(mockPlugin.getState()).thenReturn(PluginState.RUNNING);

        // Act & Assert
        assertThrows(PluginException.class, () -> {
            pluginManager.uninstallPlugin("test-plugin", false);
        });
    }

    @Test
    void testIsPluginLoaded() {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));

        // Act
        boolean result = pluginManager.isPluginLoaded("test-plugin");

        // Assert
        assertTrue(result);
        verify(pluginRegistry).getPlugin("test-plugin");
    }

    @Test
    void testGetPluginState() {
        // Arrange
        when(pluginRegistry.getPlugin("test-plugin")).thenReturn(Optional.of(mockPlugin));
        when(mockPlugin.getState()).thenReturn(PluginState.RUNNING);

        // Act
        PluginState result = pluginManager.getPluginState("test-plugin");

        // Assert
        assertEquals(PluginState.RUNNING, result);
    }

    @Test
    void testStart() throws Exception {
        // Arrange
        when(pluginRegistry.getAllPlugins()).thenReturn(Arrays.asList(mockPlugin));

        // Act
        pluginManager.start();

        // Assert
        verify(lifecycleManager).initializePlugins(eq(Arrays.asList(mockPlugin)), any(PluginContext.class));
        verify(lifecycleManager).startPlugins(Arrays.asList(mockPlugin));
    }
}
