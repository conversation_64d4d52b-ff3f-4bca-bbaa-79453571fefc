package com.talkweb.ai.indexer.core.classloader;

import java.net.URLClassLoader;
import java.nio.file.Path;
import java.util.Optional;

/**
 * 插件类加载器管理器接口
 * 负责管理插件类加载器的创建、缓存和清理
 */
public interface PluginClassLoaderManager {

    /**
     * 为指定插件创建类加载器
     * 
     * @param pluginId 插件ID
     * @param pluginPath 插件文件路径
     * @return 类加载器实例
     * @throws ClassLoaderException 如果创建失败
     */
    URLClassLoader createClassLoader(String pluginId, Path pluginPath) throws ClassLoaderException;

    /**
     * 获取指定插件的类加载器
     * 
     * @param pluginId 插件ID
     * @return 类加载器实例，如果不存在则返回空
     */
    Optional<URLClassLoader> getClassLoader(String pluginId);

    /**
     * 移除并清理指定插件的类加载器
     * 
     * @param pluginId 插件ID
     * @return 如果成功移除返回true
     */
    boolean removeClassLoader(String pluginId);

    /**
     * 清理所有类加载器
     */
    void clearAllClassLoaders();

    /**
     * 检查指定插件是否有类加载器
     * 
     * @param pluginId 插件ID
     * @return 如果存在返回true
     */
    boolean hasClassLoader(String pluginId);

    /**
     * 获取类加载器统计信息
     * 
     * @return 统计信息
     */
    ClassLoaderStatistics getStatistics();

    /**
     * 强制垃圾回收类加载器
     * 
     * @param pluginId 插件ID
     */
    void forceGarbageCollection(String pluginId);

    /**
     * 类加载器统计信息
     */
    class ClassLoaderStatistics {
        private final int totalClassLoaders;
        private final int activeClassLoaders;
        private final long totalMemoryUsage;
        private final int totalLoadedClasses;

        public ClassLoaderStatistics(int totalClassLoaders, int activeClassLoaders, 
                                   long totalMemoryUsage, int totalLoadedClasses) {
            this.totalClassLoaders = totalClassLoaders;
            this.activeClassLoaders = activeClassLoaders;
            this.totalMemoryUsage = totalMemoryUsage;
            this.totalLoadedClasses = totalLoadedClasses;
        }

        public int getTotalClassLoaders() {
            return totalClassLoaders;
        }

        public int getActiveClassLoaders() {
            return activeClassLoaders;
        }

        public long getTotalMemoryUsage() {
            return totalMemoryUsage;
        }

        public int getTotalLoadedClasses() {
            return totalLoadedClasses;
        }
    }
}
