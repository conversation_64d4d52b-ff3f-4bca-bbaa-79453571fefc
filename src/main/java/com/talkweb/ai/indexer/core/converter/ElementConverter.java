package com.talkweb.ai.indexer.core.converter;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import org.jsoup.nodes.Element;

import java.util.Set;

/**
 * 元素转换器接口，继承自BaseConverter，用于将HTML元素转换为Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public interface ElementConverter extends BaseConverter<Element, String> {
    
    /**
     * 获取支持的HTML标签集合
     * 
     * @return 支持的HTML标签集合
     */
    Set<String> getSupportedTags();
    
    /**
     * 获取转换器优先级（优先级越高越先被尝试）
     * 
     * @return 转换器优先级
     */
    default int getPriority() {
        return 0;
    }
    
    /**
     * 检查是否可以转换指定的元素
     * 
     * @param element 元素
     * @param context 转换上下文
     * @return 如果可以转换返回true，否则返回false
     */
    @Override
    default boolean supports(Element element, ConversionContext context) {
        return element != null && getSupportedTags().contains(element.tagName().toLowerCase());
    }
    
    /**
     * 执行转换操作
     * 
     * @param element 元素
     * @param context 转换上下文
     * @return 转换结果
     * @throws ConversionException 转换异常
     */
    @Override
    String convert(Element element, ConversionContext context) throws ConversionException;
    
    /**
     * 转换元素（兼容旧接口）
     * 
     * @param element 元素
     * @param builder Markdown构建器
     * @param context 转换上下文
     * @throws com.talkweb.ai.indexer.util.markdown.converter.ElementConverter.ConversionException 转换异常
     * @deprecated 使用 {@link #convert(Element, ConversionContext)} 代替
     */
    @Deprecated
    default void convert(Element element, MarkdownBuilder builder, 
            com.talkweb.ai.indexer.util.markdown.ConversionContext context) 
            throws com.talkweb.ai.indexer.util.markdown.converter.ElementConverter.ConversionException {
        try {
            // 创建适配的上下文
            ConversionContext adaptedContext = adaptContext(context);
            
            // 执行转换
            String result = convert(element, adaptedContext);
            
            // 将结果添加到构建器
            if (result != null && !result.isEmpty()) {
                builder.append(result);
            }
        } catch (ConversionException e) {
            throw new com.talkweb.ai.indexer.util.markdown.converter.ElementConverter.ConversionException(
                    e.getMessage(), e.getCause());
        }
    }
    
    /**
     * 将旧的上下文适配为新的上下文
     * 
     * @param oldContext 旧的上下文
     * @return 新的上下文
     */
    default ConversionContext adaptContext(com.talkweb.ai.indexer.util.markdown.ConversionContext oldContext) {
        ConversionContext.Builder builder = ConversionContext.builder();
        
        // 适配模式
        builder.mode(oldContext.getMode().name());
        
        // 适配选项
        ConversionOptions.Builder optionsBuilder = ConversionOptions.builder();
        oldContext.getProperties().forEach(optionsBuilder::option);
        builder.options(optionsBuilder.build());
        
        // 适配属性
        oldContext.getProperties().forEach(builder::property);
        
        return builder.build();
    }
    
    /**
     * 检查是否应该处理子元素
     * 
     * @param element 元素
     * @param context 转换上下文
     * @return 如果应该处理子元素返回true，否则返回false
     */
    default boolean shouldProcessChildren(Element element, ConversionContext context) {
        return true;
    }
    
    /**
     * 转换前的处理钩子
     * 
     * @param element 元素
     * @param context 转换上下文
     */
    default void beforeConvert(Element element, ConversionContext context) {
        // 默认实现不做任何操作
    }
    
    /**
     * 转换后的处理钩子
     * 
     * @param element 元素
     * @param context 转换上下文
     */
    default void afterConvert(Element element, ConversionContext context) {
        // 默认实现不做任何操作
    }
}